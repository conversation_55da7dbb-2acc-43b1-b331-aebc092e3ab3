<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bajaj Motors - Bike Details</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto&display=swap"
      rel="stylesheet"
    />

    <script>
      tailwind.config = {
        theme: {
          extend: {
            animation: {
              highlight: "highlight 1.5s ease-in-out infinite alternate",
            },
            keyframes: {
              highlight: {
                from: { boxShadow: "0 0 5px rgba(0, 71, 171, 0.5)" },
                to: { boxShadow: "0 0 20px rgba(0, 71, 171, 0.8)" },
              },
            },
            fontFamily: {
              roboto: ["Roboto", "sans-serif"],
              robotoFlex: ["Roboto Flex", "sans-serif"],
            },
          },
        },
      };
    </script>

    <style>
      /* Top Bar Responsive Styles */
      .top-bar {
        padding-left: 1rem;
        padding-right: 1rem;
      }

      /* Desktop only: constrained width for top-bar */
      @media (min-width: 1024px) {
        .top-bar {
          padding-left: 150px;
          padding-right: 150px;
        }
      }

      /* Transparent top bar */
      .top-bar-transparent {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      /* Top bar content responsive adjustments */
      .top-bar-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .top-bar-left {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .top-bar-right {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      /* Top bar text responsive sizing */
      .top-bar-text {
        font-size: 0.875rem;
        font-weight: bold;
        color: #fafafa;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* Top bar logo responsive sizing */
      .top-bar-logo {
        height: 2rem;
        width: auto;
        flex-shrink: 0;
      }

      /* Top bar icon responsive sizing */
      .top-bar-icon {
        width: 23px;
        height: 18px;
        flex-shrink: 0;
      }

      .floating-navbar {
        margin: 0 auto;
        height: 110px;
        padding-left: 0;
        padding-right: 0;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      /* Desktop only: constrained width */
      @media (min-width: 1024px) {
        .floating-navbar {
          margin-left: 150px;
          margin-right: 150px;
        }
      }

      .nav-bg {
        background: linear-gradient(to bottom, #ffffff 0%, #ebebeb 100%);
      }

      .model-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
      }

      .category-btn.active {
        color: #222222;
        text-decoration: underline;
        text-underline-offset: 4px;
      }

      .tab-btn.active {
        color: #222222;
        border-bottom: 2px solid #222222;
      }

      .mobile-model-item:hover {
        background-color: #f9fafb;
      }

      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }

      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }

      .media-dropdown {
        min-width: 200px;
      }

      /* Fixed overlay stacking context */
      #mobile-overlay {
        z-index: 40;
        /* Below category detail */
      }

      #mobile-category-detail {
        z-index: 60;
        /* Above overlay */
      }

      /* Improved mobile navigation */
      .mobile-nav-section {
        transition: all 0.3s ease;
      }

      /* Mega Menu Fixed Height */
      .mega-menu-container {
        height: 500px;
        /* Fixed height */
        overflow: hidden;
        /* Hide scrollbars for the container */
      }

      .mega-menu-scrollable {
        height: 100%;
        overflow-y: auto;
        /* Enable scrolling for content */
        scrollbar-width: thin;
        scrollbar-color: #0047ab #f1f1f1;
      }

      .mega-menu-scrollable::-webkit-scrollbar {
        width: 8px;
      }

      .mega-menu-scrollable::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      .mega-menu-scrollable::-webkit-scrollbar-thumb {
        background: #0047ab;
        border-radius: 4px;
      }

      .mega-menu-scrollable::-webkit-scrollbar-thumb:hover {
        background: #003380;
      }

      .categories-sidebar {
        height: 100%;
        overflow-y: auto;
      }

      .models-section {
        height: calc(100% - 50px);
        /* Account for tabs height */
      }

      /* Category heading styles */
      .category-heading {
        grid-column: 1 / -1;
        text-align: left;
        padding: 10px 0 5px;
        font-weight: 600;
        /* color: #0047ab; */
        margin-top: 15px;
        width: fit-content;
        /* border-bottom: 1px solid #e5e7eb; */
      }

      .category-heading:first-child {
        margin-top: 0;
      }

      /* Bouncing Scroll Indicator */
      .scroll-indicator {
        position: absolute;
        bottom: 100px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 10;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .scroll-indicator:hover {
        transform: translateX(-50%) scale(1.1);
      }

      .grid-item {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        transition: transform 0.3s ease;
      }

      .grid-item:hover {
        transform: scale(1.02);
      }

      .grid-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
        border-radius: 8px;
      }

      .grid-item:hover img {
        transform: scale(1.05);
      }

      .overlay {
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, 0.4);
        transition: background 0.3s ease;
        border-radius: 8px;
      }

      .grid-item:hover .overlay {
        background: rgba(0, 0, 0, 0.6);
      }

      .title {
        position: absolute;
        top: 16px;
        left: 16px;
        right: 16px;
        color: white;
        font-weight: bold;
        font-size: 14px;
        line-height: 1.2;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
      }

      .description {
        position: absolute;
        bottom: 16px;
        left: 16px;
        right: 16px;
        color: white;
        font-size: 12px;
        line-height: 1.4;
        opacity: 0;
        transform: translateY(16px);
        transition: all 0.3s ease;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
      }

      .grid-item:hover .description {
        opacity: 1;
        transform: translateY(0);
      }

      @media (max-width: 768px) {
        .title {
          font-size: 12px;
        }

        .description {
          font-size: 11px;
        }
      }

      @media (max-width: 480px) {
        .title {
          font-size: 11px;
        }

        .description {
          font-size: 10px;
        }
      }
    </style>
    <style type="text/tailwindcss">
      @layer utilities {
        .grid-container {
          display: grid;
          grid-template-columns: 1fr; /* Single column for mobile */
          grid-auto-rows: minmax(200px, auto);
          gap: 0.5rem;
          height: auto; /* Let height adjust dynamically */
        }

        /* Small screens (min-width: 640px) */
        @media (min-width: 640px) {
          .grid-container {
            grid-template-columns: repeat(2, 1fr); /* Two columns */
            gap: 0.75rem;
          }
        }

        /* Medium screens (min-width: 768px) */
        @media (min-width: 768px) {
          .grid-container {
            grid-template-columns: repeat(4, 1fr); /* Four columns */
            grid-template-rows: minmax(150px, auto);
            gap: 1rem;
          }
        }

        /* Large screens (min-width: 1024px) */
        @media (min-width: 1024px) {
          .grid-container {
            grid-template-columns: repeat(6, 1fr); /* Six columns */
            grid-template-rows: repeat(16, minmax(50px, auto));
            min-height: 600px; /* Minimum height to avoid collapse, adjustable */
            gap: 1rem;
          }
        }

        .grid-item {
          @apply relative overflow-hidden rounded-lg shadow-md transition-all duration-300 cursor-pointer;
        }

        .grid-item:hover {
          @apply -translate-y-1 shadow-xl;
        }

        .grid-item img {
          @apply w-full h-full object-cover transition-transform duration-500;
        }

        .grid-item:hover img {
          transform: scale(1.05);
        }

        .overlay {
          @apply absolute bottom-0 left-0 right-0 p-4 text-white;
          background: linear-gradient(
            to top,
            rgba(0, 0, 0, 0.8) 0%,
            rgba(0, 0, 0, 0) 100%
          );
        }

        .overlay .description {
          opacity: 0; /* Hide description by default */
          transition: opacity 0.3s ease-in-out;
        }

        .grid-item:hover .description {
          opacity: 1; /* Show description on hover */
        }

        .feature-highlight {
          animation: highlight 1.5s ease-in-out infinite alternate;
        }
      }
    </style>
    <style>
      .bike-image {
        transition: opacity 0.3s ease;
      }

      .bike-image.hidden {
        opacity: 0;
      }

      .bike-image:not(.hidden) {
        opacity: 1;
      }

      @media (max-width: 1024px) {
        .bike-image {
          max-height: 350px;
        }
      }

      @media (max-width: 768px) {
        .bike-image {
          max-height: 250px;
        }
      }

      @media (max-width: 480px) {
        .bike-image {
          max-height: 200px;
        }
      }
    </style>

    <style>
      /* Ensure the section takes full height and width */
      .h-screen {
        min-height: 100vh;
      }

      /* Remove any padding that reduces height */
      .relative.z-10 {
        padding: 0;
      }

      /* Optional: Adjust gradient if needed */
      .bg-gradient-to-b {
        opacity: 0.7;
        /* Reduce gradient visibility if desired */
      }

      /* Responsive adjustments */
      @media (max-width: 1024px) {
        .h-screen {
          min-height: 80vh;
          /* Adjust for smaller screens if needed */
        }
      }

      @media (max-width: 768px) {
        .h-screen {
          min-height: 60vh;
          /* Further adjust for mobile */
        }
      }

      /* Custom keyframes for spinner animation */
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }

      /* Ensure canvas fits container and add subtle platform ring */
      .spritespin-canvas {
        width: 100% !important;
        height: 100% !important;
        object-fit: contain;
      }

      .platform-ring {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80%;
        height: 20px;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.15) 0%,
          transparent 70%
        );
        border-radius: 50%;
        filter: blur(2px);
      }
    </style>
  </head>

  <body class="font-roboto">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50">
      <!-- Top Bar -->
      <div class="py-2 top-bar top-bar-transparent">
        <div class="top-bar-content">
          <div class="top-bar-left">
            <img
              src="assets/golcha-logo.png"
              alt="golcha_logo"
              class="top-bar-logo"
            />
            <span class="top-bar-text"
              >GOLCHHA GROUP WITH LEGACY OF 100 YEAR</span
            >
          </div>
          <div class="top-bar-right">
            <img src="./assets/globe.png" alt="globe" class="top-bar-icon" />
            <span class="top-bar-text">International website</span>
          </div>
        </div>
      </div>

      <!-- Main Navbar -->
      <nav class="nav-bg floating-navbar bg-white relative">
        <div class="px-6">
          <div class="flex justify-between items-center h-[110px]">
            <!-- Mobile Menu Button -->
            <button id="mobile-menu-btn" class="lg:hidden p-2">
              <i class="fas fa-bars text-xl text-gray-700"></i>
            </button>

            <!-- Desktop Navigation - Left Side -->
            <div class="hidden lg:flex items-center space-x-8">
              <!-- Motorcycles Dropdown -->
              <div class="relative group">
                <button class="flex items-center space-x-1 hover:text-accent">
                  <span>MOTORCYCLES</span>
                  <i class="fas fa-chevron-down text-xs"></i>
                </button>

                <!-- Mega Dropdown - Fixed Height Container -->
                <div
                  class="absolute top-full left-0 w-screen max-w-5xl bg-white shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 mega-menu-container"
                >
                  <div class="flex h-full">
                    <!-- Categories Sidebar - Scrollable -->
                    <div class="w-64 bg-gray-100 p-4 categories-sidebar">
                      <div class="space-y-2">
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent active"
                          data-category="pulsar"
                        >
                          PULSAR
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="dominar"
                        >
                          DOMINAR
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium over:text-accent"
                          data-category="avengers"
                        >
                          AVENGERS
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="discover"
                        >
                          DISCOVER
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="platina"
                        >
                          PLATINA
                        </button>
                      </div>
                    </div>

                    <!-- Models Section - Scrollable -->
                    <div class="mega-menu-scrollable flex-1 flex flex-col">
                      <div class="p-6">
                        <!-- Category Tabs -->
                        <div
                          id="tabs-container"
                          class="flex space-x-6 mb-4 text-sm boder-b-2"
                        >
                          <!-- Tabs will be dynamically generated -->
                        </div>

                        <!-- Models Content -->
                        <div
                          id="models-content"
                          class="model-grid gap-4 models-section"
                        >
                          <!-- Models will be populated by JavaScript -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <a href="#" class="hover:text-accent flex items-center"
                >SHOWROOMS</a
              >
              <a href="#" class="hover:text-accent flex items-center"
                >WORKSHOPS</a
              >
              <a href="#" class="hover:text-accent flex items-center hide-1150"
                >EVENTS</a
              >
            </div>

            <!-- Centered Logo -->
            <div
              class="flex-1 flex justify-center items-center min-w-0 flex-shrink-0"
            >
              <a
                href="/index.html"
                class="flex items-center flex-shrink-0 min-w-[120px] justify-center"
              >
                <img class="main-logo px-4" src="assets/logo.png" alt="logo" />
              </a>
            </div>

            <!-- Desktop Navigation - Right Side -->
            <div class="hidden lg:flex items-center space-x-8">
              <a
                href="/book-test-ride.html"
                class="hover:text-accent flex items-center"
                >BOOK TEST RIDE</a
              >
              <a
                href="/about.html"
                class="hover:text-accent flex items-center hide-1150"
                >ABOUT US</a
              >
              <a href="#" class="hover:text-accent flex items-center">NEWS</a>

              <!-- Media Center Dropdown - Desktop -->
              <div class="relative group">
                <button class="flex items-center space-x-1 hover:text-accent">
                  <span>MEDIA CENTER</span>
                  <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div
                  class="absolute top-full left-0 w-48 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 media-dropdown"
                >
                  <div class="py-2">
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >ABOUT US</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >ANNOUNCEMENTS</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >EVENTS</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >BLOGS</a
                    >
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >DOWNLOAD CENTER</a
                    >
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >CONTACT US</a
                    >
                    <a
                      href="faqs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >FAQS</a
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Mobile BIKES Button -->
            <div class="lg:hidden relative">
              <button
                id="mobile-bikes-btn"
                class="text-gray-700 font-medium flex items-center space-x-1"
              >
                <span>BIKES</span>
                <i class="fas fa-chevron-down text-xs"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile Menu Overlay - Fixed z-index -->
        <div
          id="mobile-overlay"
          class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 hidden"
        ></div>

        <!-- Mobile Menu -->
        <div
          id="mobile-menu"
          class="lg:hidden mobile-nav-section fixed top-0 left-0 w-80 h-screen bg-white z-50 transform -translate-x-full transition-transform duration-300 hidden"
        >
          <div class="p-4 bg-white">
            <button
              id="close-mobile-menu"
              class="absolute top-4 right-4 text-xl text-gray-600"
            >
              <i class="fas fa-times"></i>
            </button>

            <!-- Mobile Menu Items -->
            <div class="mt-8 space-y-4">
              <!-- Mobile Motorcycles Dropdown -->
              <div class="mobile-dropdown">
                <button
                  class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2"
                >
                  <span>BIKES</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </button>
                <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="pulsar"
                  >
                    PULSAR
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="dominar"
                  >
                    DOMINAR
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="avengers"
                  >
                    AVENGERS
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="discover"
                  >
                    DISCOVER
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="platina"
                  >
                    PLATINA
                  </button>
                </div>
              </div>

              <a href="#" class="block py-2 font-medium">SHOWROOMS</a>
              <a href="#" class="block py-2 font-medium">WORKSHOPS</a>
              <a href="#" class="block py-2 font-medium">EVENTS</a>
              <a href="#" class="block py-2 font-medium">BOOK TEST RIDE</a>
              <a href="#" class="block py-2 font-medium">ABOUT US</a>
              <a href="#" class="block py-2 font-medium">NEWS</a>

              <!-- Media Center Dropdown - Mobile -->
              <div class="mobile-dropdown">
                <button
                  class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2"
                >
                  <span>MEDIA CENTER</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </button>
                <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >ABOUT US</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >ANNOUNCEMENTS</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >EVENTS</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600">BLOGS</a>
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >DOWNLOAD CENTER</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >CONTACT US</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600">FAQS</a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile Category Detail View - Fixed z-index -->
        <div
          id="mobile-category-detail"
          class="lg:hidden mobile-nav-section fixed top-0 left-0 w-80 h-screen bg-white z-50 transform -translate-x-full transition-transform duration-300 hidden"
        >
          <div class="p-4">
            <div class="flex items-center mb-4">
              <button id="back-to-categories" class="mr-3 text-gray-600">
                <i class="fas fa-chevron-left"></i>
              </button>
              <button
                id="close-category-detail"
                class="absolute top-4 right-4 text-xl text-gray-600"
              >
                <i class="fas fa-times"></i>
              </button>
              <span id="category-title" class="font-medium text-gray-800"
                >BIKES</span
              >
            </div>

            <!-- Category Tabs -->
            <div
              id="mobile-tabs-container"
              class="flex space-x-4 mb-4 text-sm border-b pb-2"
            >
              <!-- Tabs will be dynamically generated -->
            </div>

            <!-- Mobile Models List -->
            <div
              id="mobile-models-list"
              class="space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto scrollbar-hide"
            >
              <!-- Models will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Mobile BIKES Sheet - Same behavior as hamburger menu -->
        <div
          id="mobile-bikes-sheet"
          class="lg:hidden mobile-nav-section fixed top-0 right-0 w-80 h-screen bg-white z-50 transform translate-x-full transition-transform duration-300 hidden"
        >
          <div class="p-4 bg-white relative">
            <div class="flex items-center justify-between mb-4">
              <span
                id="mobile-bikes-header"
                class="font-medium text-gray-800 uppercase"
                >Bikes</span
              >
              <button id="close-bikes-sheet" class="text-xl text-gray-600">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <button
              id="back-to-bikes-categories"
              class="hidden items-center text-sm text-gray-600 mb-4"
            >
              <i class="fas fa-chevron-left mr-2"></i>
              Back
            </button>

            <!-- Categories List -->
            <div id="mobile-bikes-categories-list" class="space-y-1 mt-4">
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="pulsar"
              >
                <span>PULSAR</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="dominar"
              >
                <span>DOMINAR</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="avengers"
              >
                <span>AVENGERS</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="discover"
              >
                <span>DISCOVER</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="platina"
              >
                <span>PLATINA</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
            </div>

            <!-- Category Tabs (hidden initially) -->
            <div
              id="mobile-bikes-tabs-container"
              class="hidden flex gap-x-4 gap-y-2 mb-4 text-sm border-b pb-2"
            >
              <!-- Tabs will be dynamically generated -->
            </div>

            <!-- Mobile Bikes Models List (hidden initially) -->
            <div
              id="mobile-bikes-models-content"
              class="hidden space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto scrollbar-hide"
            >
              <!-- Models will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </nav>
    </header>

    <!-- Hero Section -->
    <section class="relative h-screen overflow-hidden">
      <div
        class="absolute inset-0 bg-gradient-to-b from-gray-100 to-white"
      ></div>
      <div class="relative z-10 h-full w-full flex items-center justify-center">
        <img
          src="assets/bike-detail.png"
          alt="Pulsar NS200"
          class="w-full h-full object-cover"
        />
      </div>
    </section>

    <!-- 360° Viewer Section -->
    <section class="bg-white min-h-screen flex items-center justify-center">
      <!-- Background image using <img> tag -->
      <img
        src="assets/360/360ImgBg.png"
        alt="Mountain background"
        class="absolute inset-0 w-full h-[50%] object-cover z-0 pointer-events-none"
      />
      <div class="relative z-10 w-full max-w-[800px] mx-auto p-4">
        <!-- 360 Viewer Container -->
        <div
          id="bike-viewer"
          class="relative w-full h-[500px] mx-auto overflow-hidden loading"
        >
          <div
            class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center font-sans text-gray-700"
          >
            <div
              class="w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-2"
            ></div>
            <div id="loading-text">Loading 360° View...</div>
            <div class="w-[100px] h-1 bg-gray-200 mt-2">
              <div
                class="progress-bar h-full bg-blue-500 w-0 transition-all duration-300"
              ></div>
            </div>
          </div>
          <!-- Subtle platform ring -->
          <div class="platform-ring"></div>
        </div>
        <!-- 360° Label (integrated into arc) -->
        <div
          class="absolute bottom-20 left-1/2 -translate-x-1/2 w-[90%] lg:w-full"
        >
          <img src="assets/360/360.svg" alt="360 degrees" class="w-full" />
        </div>
        <!-- Pricing and Info -->
        <div class="text-center mt-6 text-gray-700 font-sans">
          <p class="text-xl font-bold">Starts at 4,98,000</p>
          <p class="text-sm">
            Available at all authorized HH Bajaj showrooms across Nepal
          </p>
        </div>
      </div>
    </section>

    <!-- Bike Details Section -->
    <section class="py-8">
      <!-- Key Features -->
      <div
        class="max-w-7xl mx-auto px-4 py-8 flex flex-col lg:flex-row items-center justify-between gap-12"
      >
        <!-- Left Content -->
        <div class="lg:w-1/3">
          <h1 class="text-4xl md:text-5xl font-extrabold text-gray-900 mb-4">
            Experience the <br />All-New Pulsar N250
          </h1>
          <p class="text-gray-700 max-w-lg mb-4">
            Unleash the thrill with the Bajaj Pulsar N250—featuring a powerful
            249.07 cc engine delivering 24.5 PS at 8,750 RPM and 21.5 Nm torque
            at 6,500 RPM.
          </p>
          <p class="text-gray-700 max-w-lg mb-4">
            Equipped with dual-channel ABS, assist & slipper clutch, and
            advanced suspension, it ensures a dynamic and safe ride. The sleek
            design, LED projector headlamp, and Bluetooth-enabled digital
            console add to its modern appeal.
          </p>
          <p class="text-gray-700 max-w-lg mb-6">
            Available in multiple colors.
          </p>

          <button
            class="bg-black text-white px-6 py-2 rounded-full hover:bg-gray-800 transition"
          >
            BOOK TEST RIDE →
          </button>

          <div class="mt-8 text-sm text-gray-600">
            Discover the full potential of the Bajaj Pulsar N250.
            <br />
            <a
              href="#"
              class="inline-flex items-center text-blue-600 hover:underline mt-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Download Brochure
            </a>
          </div>
        </div>

        <!-- Right Image and Color Options -->
        <div class="flex-1 relative" data-bike="pulsar-n250">
          <!-- Bike Images -->
          <div class="relative w-full">
            <img
              id="img-pulsar-n250-black"
              src="/assets/bikes/pulsar/pulsar_125.png"
              alt="Pulsar N250 in Brooklyn Black"
              class="bike-image w-full h-auto object-contain max-h-[500px]"
              data-color="black"
              data-color-name="Brooklyn Black"
            />
            <img
              id="img-pulsar-n250-white"
              src="/assets/bikes/pulsar/pulsar250.png"
              alt="Pulsar N250 in Pearl Metallic White"
              class="bike-image w-full h-auto object-contain max-h-[500px] hidden"
              data-color="white"
              data-color-name="Pearl Metallic White"
            />
            <img
              id="img-pulsar-n250-green"
              src="/assets/bikes/pulsar/pulsar_ns_250.png"
              alt="Pulsar N250 in Gloss Racing Green"
              class="bike-image w-full h-auto object-contain max-h-[500px] hidden"
              data-color="green"
              data-color-name="Gloss Racing Green"
            />
          </div>

          <!-- Color Options -->
          <div class="flex justify-end items-center gap-4 mt-4">
            <span class="text-sm font-medium text-gray-700" id="color-label"
              >Brooklyn Black</span
            >
            <div class="flex items-center space-x-2">
              <button
                data-color="black"
                class="color-btn w-6 h-6 rounded-full border-2 border-black"
                style="background-color: black"
                aria-label="Select Brooklyn Black"
              ></button>
              <button
                data-color="white"
                class="color-btn w-6 h-6 rounded-full border border-gray-300"
                style="background-color: white"
                aria-label="Select Pearl Metallic White"
              ></button>
              <button
                data-color="green"
                class="color-btn w-6 h-6 rounded-full border border-gray-300"
                style="background-color: green"
                aria-label="Select Gloss Racing Green"
              ></button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!---------------------Grid Images  --------------------------->
    <section>
      <div class="max-w-7xl mx-auto p-4 sm:p-6">
        <!-- Header -->
        <div class="text-center mb-6 sm:mb-8">
          <h1 class="text-4xl sm:text-2xl md:text-3xl font-bold text-gray-800">
            PULSAR N250 OVERVIEW
          </h1>
        </div>

        <!-- Responsive Grid Container -->
        <div
          class="grid-container w-full max-w-7xl mx-auto overflow-hidden mb-6 sm:mb-8"
        >
          <!-- Item 1: Engine -->
          <div
            class="grid-item col-span-1 sm:col-span-2 md:col-span-2 lg:col-span-2 lg:row-span-8 feature-highlight"
          >
            <img src="/assets/bike-overview/OI1.png" alt="Refined Engine" />
            <div class="overlay">
              <div class="font-bold text-xs sm:text-sm md:text-base">
                ALL-NEW REFINED ENGINE
              </div>
              <div class="description text-lg font-semibold text-gray-300 mt-1">
                Experience the thrill of a powerful, refined engine designed for
                optimal performance and efficiency.
              </div>
            </div>
          </div>

          <!-- Item 2: Exhaust -->
          <div
            class="grid-item col-span-1 sm:col-span-2 md:col-span-4 lg:col-span-4 lg:row-span-4 lg:col-start-3"
          >
            <img src="/assets/bike-overview/OI2.png" alt="Sporty Exhaust" />
            <div class="overlay">
              <div class="font-bold text-xs sm:text-sm md:text-base">
                SPORTY UNDERBELLY EXHAUST
              </div>
              <div class="description text-lg font-semibold text-gray-300 mt-1">
                The sleek underbelly exhaust not only looks great but also
                contributes to better mass centralization.
              </div>
            </div>
          </div>

          <!-- Item 3: ABS -->
          <div
            class="grid-item col-span-1 sm:col-span-1 md:col-span-2 lg:col-span-2 lg:row-span-6 lg:col-start-3 lg:row-start-5"
          >
            <img src="/assets/bike-overview/OI4.png" alt="ABS Braking" />
            <div class="overlay">
              <div class="font-bold text-xs sm:text-sm md:text-base">
                DUAL CHANNEL ABS
              </div>
              <div class="description text-lg font-semibold text-gray-300 mt-1">
                Enhanced safety with dual-channel ABS, providing superior
                braking control in all conditions.
              </div>
            </div>
          </div>

          <!-- Item 4: Colors -->
          <div
            class="grid-item col-span-1 sm:col-span-1 md:col-span-2 lg:col-span-2 lg:row-span-6 lg:col-start-5 lg:row-start-5"
          >
            <img src="/assets/bike-overview/OI5.png" alt="Color Options" />
            <div class="overlay">
              <div class="font-bold text-xs sm:text-sm md:text-base">
                RED, BLUE, BLACK
              </div>
              <div class="description text-lg font-semibold text-gray-300 mt-1">
                Available in striking color options: fiery Red, vibrant Blue,
                and classic Black.
              </div>
            </div>
          </div>

          <!-- Item 5: Suspension -->
          <div
            class="grid-item col-span-1 sm:col-span-2 md:col-span-2 lg:col-span-2 lg:row-span-8 lg:row-start-9"
          >
            <img src="/assets/bike-overview/OI3.png" alt="Suspension" />
            <div class="overlay">
              <div class="font-bold text-xs sm:text-sm md:text-base">
                BEST IN CLASS SUSPENSION
              </div>
              <div class="description text-lg font-semibold text-gray-300 mt-1">
                Enjoy a smooth and comfortable ride with best-in-class
                suspension, designed to absorb bumps and offer stability.
              </div>
            </div>
          </div>

          <!-- Item 6: Torque -->
          <div
            class="grid-item col-span-1 sm:col-span-1 md:col-span-2 lg:col-span-2 lg:row-span-6 lg:col-start-3 lg:row-start-11"
          >
            <img src="/assets/bike-overview/OI6.png" alt="Torque" />
            <div class="overlay">
              <div class="font-bold text-xs sm:text-sm md:text-base">
                TORQUE ON DEMAND
              </div>
              <div class="description text-lg font-semibold text-gray-300 mt-1">
                Unleash instant power with torque on demand, ensuring quick
                acceleration and responsive handling.
              </div>
            </div>
          </div>

          <!-- Item 7: Dual Tone -->
          <div
            class="grid-item col-span-1 sm:col-span-1 md:col-span-2 lg:col-span-2 lg:row-span-6 lg:col-start-5 lg:row-start-11"
          >
            <img src="/assets/bike-overview/OI7.png" alt="Dual Tone" />
            <div class="overlay">
              <div class="font-bold text-xs sm:text-sm md:text-base">
                DUAL TONE
              </div>
              <div class="description text-lg font-semibold text-gray-300 mt-1">
                A sophisticated dual-tone finish adds a premium look and feel to
                the motorcycle.
              </div>
            </div>
          </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
          <h2
            class="text-lg sm:text-xl md:text-2xl font-bold text-gray-800 mb-4"
          >
            GIVE A TEST RIDE
          </h2>
          <p class="text-gray-600 text-xs sm:text-sm mb-6 max-w-md mx-auto">
            Experience the thrill of the Bajaj Pulsar N250 - a perfect blend of
            power, technology, and style. Contact us and book your test ride
            today.
          </p>
          <a
            href="/book-test-ride.html"
            class="bg-black text-white px-4 sm:px-6 md:px-8 py-2 md:py-3 rounded-md font-semibold hover:bg-gray-800 transition-colors duration-300"
          >
            BOOK TEST RIDE
          </a>
        </div>
      </div>
    </section>

    <!-------------------- Key Highlights Section ------------------->
    <section>
      <div class="max-w-7xl mx-auto p-4 md:p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">KEY HIGHLIGHTS</h2>

        <!-- Tab Buttons -->
        <div id="tabButtons" class="flex space-x-1 mb-6">
          <button
            data-tab="performance"
            class="px-3 py-2 text-sm font-medium border-b-2 border-black text-black transition-all duration-200"
          >
            PERFORMANCE
          </button>
          <button
            data-tab="design"
            class="px-3 py-2 text-sm font-medium border-b-2 border-transparent text-gray-600 transition-all duration-200"
          >
            DESIGN
          </button>
          <button
            data-tab="tech"
            class="px-3 py-2 text-sm font-medium border-b-2 border-transparent text-gray-600 transition-all duration-200"
          >
            TECH
          </button>
          <button
            data-tab="safety"
            class="px-3 py-2 text-sm font-medium border-b-2 border-transparent text-gray-600 transition-all duration-200"
          >
            SAFETY
          </button>
        </div>

        <!-- Grid Layout -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          <!-- Left: Tab Content -->
          <div class="order-2 lg:order-1">
            <!-- Performance Tab Content -->
            <div id="tab-performance" class="tab-content space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Engine Type</span>
                <span class="font-semibold">249cc Oil-Cooled Engine</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Clutch</span>
                <span class="font-semibold">5-Speed Slipper Clutch</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Quick Acceleration</span>
                <span class="font-semibold">0 to 60 km/h in 3.25 seconds</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Power</span>
                <span class="font-semibold">18.5kw / 9800 rpm</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Top Speed</span>
                <span class="font-semibold">140 km/h</span>
              </div>
            </div>

            <!-- Design Tab Content -->
            <div id="tab-design" class="tab-content space-y-4 hidden">
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Headlamp</span>
                <span class="font-semibold">LED Projector</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Tail Lamp</span>
                <span class="font-semibold">Signature LED Tail</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Seat</span>
                <span class="font-semibold">Split Seat Design</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Color Options</span>
                <span class="font-semibold">Red, Black, White</span>
              </div>
            </div>

            <!-- Tech Tab Content -->
            <div id="tab-tech" class="tab-content space-y-4 hidden">
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Instrument Console</span>
                <span class="font-semibold">Fully Digital Console</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Connectivity</span>
                <span class="font-semibold">Bluetooth-enabled</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">ABS</span>
                <span class="font-semibold">Dual Channel ABS</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Charging Port</span>
                <span class="font-semibold">USB Fast Charging</span>
              </div>
            </div>

            <!-- Safety Tab Content -->
            <div id="tab-safety" class="tab-content space-y-4 hidden">
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Brakes</span>
                <span class="font-semibold">Front & Rear Disc</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">ABS</span>
                <span class="font-semibold">Dual Channel ABS</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Suspension</span>
                <span class="font-semibold"
                  >Telescopic with Anti-friction Bush</span
                >
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Frame</span>
                <span class="font-semibold">Perimeter Frame</span>
              </div>
            </div>

            <div class="mt-8">
              <button
                class="bg-gray-900 text-white px-6 py-3 text-sm font-medium hover:bg-gray-800 transition-colors duration-200"
              >
                BOOK TEST RIDE →
              </button>
            </div>
          </div>

          <!-- Right: Bike Images -->
          <div
            class="order-1 lg:order-2 flex items-center justify-center relative"
          >
            <img
              id="img-performance"
              src="/assets/bikes/pulsar/pulsar_125.png"
              alt="Pulsar NS200 Performance"
              class="tab-image w-full h-auto object-contain max-h-96"
            />
            <img
              id="img-design"
              src="/assets/bikes/pulsar/pulsar_150.png"
              alt="Pulsar NS200 Design"
              class="tab-image w-full h-auto object-contain max-h-96 hidden"
            />
            <img
              id="img-tech"
              src="/assets/bikes/pulsar/pulsar250.png"
              alt="Pulsar NS200 Tech"
              class="tab-image w-full h-auto object-contain max-h-96 hidden"
            />
            <img
              id="img-safety"
              src="/assets/bikes/pulsar/pulsar_150_td.png"
              alt="Pulsar NS200 Safety"
              class="tab-image w-full h-auto object-contain max-h-96 hidden"
            />
          </div>
        </div>
      </div>
    </section>

    <!---------------------- Also visit ------------------------->
    <section class="py-10 lg:py-20 bg-gray-100">
      <div class="max-w-7xl mx-auto">
        <h1 class="text-2xl font-bold text-start mb-10">PEOPLE ALSO VISIT</h1>
        <div id="carousel" class="relative overflow-hidden">
          <div
            id="carouselInner"
            class="flex transition-transform duration-500 ease-in-out"
          >
            <div class="flex-shrink-0 w-full flex justify-around">
              <img
                src="https://via.placeholder.com/300x200"
                alt="Pulsar 220F ABS"
                class="h-48 object-cover"
              />
              <img
                src="https://via.placeholder.com/300x200"
                alt="Pulsar 150D"
                class="h-48 object-cover"
              />
              <img
                src="https://via.placeholder.com/300x200"
                alt="Pulsar 150"
                class="h-48 object-cover"
              />
            </div>
            <div class="flex-shrink-0 w-full flex justify-around">
              <img
                src="https://via.placeholder.com/300x200"
                alt="Bike 4"
                class="h-48 object-cover"
              />
              <img
                src="https://via.placeholder.com/300x200"
                alt="Bike 5"
                class="h-48 object-cover"
              />
              <img
                src="https://via.placeholder.com/300x200"
                alt="Bike 6"
                class="h-48 object-cover"
              />
            </div>
          </div>
        </div>
        <div id="indicators" class="flex justify-center mt-4 space-x-2">
          <span class="w-10 h-2 bg-blue-500 rounded-full"></span>
          <span class="w-10 h-2 bg-gray-300 rounded-full"></span>
        </div>
      </div>

      <script>
        const carouselInner = document.getElementById("carouselInner");
        const indicators = document
          .getElementById("indicators")
          .getElementsByTagName("span");
        let currentSlide = 0;
        const totalSlides = carouselInner.children.length;
        const slideInterval = 5000;

        function showSlide(index) {
          carouselInner.style.transform = `translateX(-${index * 100}%)`;
          for (let i = 0; i < indicators.length; i++) {
            indicators[i].className =
              "w-10 h-1 rounded-full " +
              (i === index ? "bg-blue-500" : "bg-gray-300");
          }
        }

        function nextSlide() {
          currentSlide = (currentSlide + 1) % totalSlides;
          showSlide(currentSlide);
        }

        setInterval(nextSlide, slideInterval);

        // Initialize
        showSlide(currentSlide);
      </script>
    </section>

    <!-- Footer  -->
    <footer class="bg-gray-50 min-h-screen flex flex-col">
      <!-- Email Signup Section -->
      <div class="flex-1 flex items-center justify-center px-4 py-12">
        <div class="max-w-md w-full">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
              Sign up for Email
            </h2>
            <p class="text-sm text-gray-500 mb-1">
              Read our
              <a href="#" class="text-blue-500 underline">privacy policy</a>
              to learn about data processing
            </p>
            <p class="text-sm text-gray-500">
              Sign up for BAJAJ latest news and updates
            </p>
          </div>

          <form id="emailForm" class="mb-4">
            <div class="flex gap-2 mb-2">
              <input
                type="email"
                id="email"
                placeholder="YOUR EMAIL ADDRESS"
                class="flex-1 bg-white border border-gray-300 rounded-md px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <button
                type="submit"
                class="bg-blue-500 text-white px-6 py-3 rounded-md text-sm font-medium hover:bg-blue-600"
              >
                SUBSCRIBE NOW
              </button>
            </div>
            <p class="text-xs text-gray-500 text-center">
              This site is protected by reCAPTCHA and the Google
              <a href="#" class="underline">Privacy Policy</a> and
              <a href="#" class="underline">Terms of Service</a> apply.
            </p>
          </form>
        </div>
      </div>

      <!-- Footer Section -->
      <div class="bg-[#0F0F0F] text-white py-12">
        <div class="max-w-6xl mx-auto px-4">
          <div class="text-center mb-8">
            <div class="flex justify-center items-center mb-4">
              <div class="w-12 h-12 mr-3 flex items-center justify-center">
                <img src="/assets/golcha-logo.png" alt="" />
              </div>
              <h3 class="text-xl font-semibold">
                GOLCHHA GROUP WITH LEGACY OF 100 YEAR
              </h3>
            </div>
          </div>

          <!-- Footer Links -->
          <div class="flex justify-center gap-8 text-sm mb-8">
            <a href="#" class="hover:text-gray-300">TERMS OF USE</a>
            <a href="#" class="hover:text-gray-300">PRIVACY INFORMATION</a>
            <a href="#" class="hover:text-gray-300">COOKIES INFORMATION</a>
          </div>

          <!-- Copyright -->
          <div class="text-center text-xs text-gray-400 mb-8">
            <p>
              Copyright © 2025 Bajaj Auto Ltd – A Sole Shareholder Company - A
              Company subject to the Management and Coordination
            </p>
            <p>activities of BAJAJ AUTO. All rights reserved. VAT NO.</p>
          </div>

          <!-- Bottom Section -->
          <div class="flex flex-wrap justify-between items-center gap-4">
            <!-- Bajaj Logo -->
            <div class="h-16 w-32">
              <img src="/assets/logo.png" alt="" />
            </div>

            <!-- Social Media Icons -->
            <div class="flex gap-4 text-2xl">
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-instagram"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-facebook"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-youtube"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-tiktok"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-twitter"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-linkedin"></i>
              </a>
            </div>

            <!-- International Website -->
            <div
              class="flex items-center gap-4 text-lg font-semibold text-white"
            >
              <img class="w-8 h-8" src="/assets/globe.png" alt="" />
              <a href="#" class="hover:text-white">International website</a>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <script>
      document
        .getElementById("emailForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          const email = document.getElementById("email").value;
          if (email) {
            alert(
              "Thank you for subscribing! You will receive updates at: " + email
            );
            document.getElementById("email").value = "";
          }
        });
    </script>

    <!-- Bike details script -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const bikeContainers = document.querySelectorAll("[data-bike]");

        bikeContainers.forEach((container) => {
          const colorButtons = container.querySelectorAll(".color-btn");
          const bikeImages = container.querySelectorAll(".bike-image");
          const colorLabel = container.querySelector("#color-label");

          function switchColor(color) {
            // Update color buttons
            colorButtons.forEach((btn) => {
              if (btn.dataset.color === color) {
                btn.classList.add("border-2", "border-black");
                btn.classList.remove("border-gray-300");
                btn.setAttribute("aria-selected", "true");
              } else {
                btn.classList.remove("border-2", "border-black");
                btn.classList.add("border", "border-gray-300");
                btn.setAttribute("aria-selected", "false");
              }
            });

            // Update bike images
            bikeImages.forEach((img) => {
              if (img.dataset.color === color) {
                img.classList.remove("hidden");
                img.setAttribute("aria-hidden", "false");
              } else {
                img.classList.add("hidden");
                img.setAttribute("aria-hidden", "true");
              }
            });

            // Update color label
            const activeImage = container.querySelector(
              `.bike-image[data-color="${color}"]`
            );
            colorLabel.textContent = activeImage.dataset.colorName;
          }

          // Add click event listeners to color buttons
          colorButtons.forEach((button) => {
            button.addEventListener("click", () =>
              switchColor(button.dataset.color)
            );
          });

          // Initialize with the first color active
          const firstColor = colorButtons[0].dataset.color;
          switchColor(firstColor);
        });
      });
    </script>

    <!-- Key highlight script -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const tabButtons = document.querySelectorAll("#tabButtons button");
        const tabContents = document.querySelectorAll(".tab-content");
        const tabImages = document.querySelectorAll(".tab-image");

        function switchTab(tabKey) {
          // Update tab buttons
          tabButtons.forEach((btn) => {
            if (btn.dataset.tab === tabKey) {
              btn.classList.remove("border-transparent", "text-gray-600");
              btn.classList.add("border-black", "text-black");
            } else {
              btn.classList.remove("border-black", "text-black");
              btn.classList.add("border-transparent", "text-gray-600");
            }
          });

          // Update tab content
          tabContents.forEach((content) => {
            if (content.id === `tab-${tabKey}`) {
              content.classList.remove("hidden");
            } else {
              content.classList.add("hidden");
            }
          });

          // Update tab images
          tabImages.forEach((img) => {
            if (img.id === `img-${tabKey}`) {
              img.classList.remove("hidden");
            } else {
              img.classList.add("hidden");
            }
          });
        }

        // Add click event listeners to tab buttons
        tabButtons.forEach((button) => {
          button.addEventListener("click", () => switchTab(button.dataset.tab));
        });

        // Initialize with performance tab active
        switchTab("performance");
      });
    </script>

    <!-- 360 degree view script -->
    <!-- Include jQuery and SpriteSpin via CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/spritespin@4.1.0/release/spritespin.min.js"></script>
    <script>
      (function ($) {
        $(document).ready(function () {
          // Verify jQuery and SpriteSpin are loaded
          if (typeof $ === "undefined") {
            showError("jQuery failed to load");
            return;
          }
          if (typeof $.fn.spritespin === "undefined") {
            showError("SpriteSpin failed to load");
            return;
          }

          const images = [
            "assets/360/1.png",
            "assets/360/2.png",
            "assets/360/3.png",
            "assets/360/4.png",
            "assets/360/5.png",
            "assets/360/6.png",
            "assets/360/7.png",
            "assets/360/8.png",
            "assets/360/9.png",
            "assets/360/10.png",
            "assets/360/11.png",
            "assets/360/12.png",
            "assets/360/13.png",
          ];

          let loadedImages = 0;
          const totalImages = images.length;
          const $container = $("#bike-viewer");
          const $progressBar = $(".progress-bar");
          const $loadingText = $("#loading-text");

          // Error handling function
          function showError(message) {
            $container.removeClass("loading").addClass("error").html(`
                        <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center font-sans text-red-500">
                            <div class="text-2xl mb-2">⚠️</div>
                            <div>${message}</div>
                            <button class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600" onclick="location.reload()">Retry</button>
                        </div>
                    `);
            console.error(message);
          }

          // Preload images with timeout and detailed logging
          function preloadImages(callback) {
            let loadAttempts = 0;
            const maxAttempts = 30; // 30 seconds timeout
            const checkInterval = 1000; // Check every 1 second

            images.forEach((src, index) => {
              const img = new Image();
              img.src = src;
              img.onload = () => {
                loadedImages++;
                updateProgress();
                console.log(`Image loaded: ${src}`);
              };
              img.onerror = () => {
                console.error(`Failed to load image: ${src}`);
                loadedImages++; // Count as loaded to avoid infinite loop
                updateProgress();
              };
            });

            function updateProgress() {
              const progress = (loadedImages / totalImages) * 100;
              $progressBar.css("width", progress + "%");
              if (loadedImages === totalImages) {
                clearInterval(timeoutId);
                callback();
              } else if (loadAttempts >= maxAttempts) {
                clearInterval(timeoutId);
                showError("Image loading timed out after 30 seconds");
              }
              loadAttempts++;
            }

            const timeoutId = setInterval(updateProgress, checkInterval);
          }

          // Initialize SpriteSpin after images are loaded
          preloadImages(() => {
            $loadingText.text("Initializing viewer..."); // Update text during initialization
            $container.removeClass("loading").empty(); // Clear loading indicator
            $container
              .spritespin({
                source: images,
                width: 800,
                height: 500,
                frameTime: 100, // For smooth manual transitions
                animate: false, // Disable auto-rotation
                sense: -1, // Reverse direction for intuitive drag
                responsive: true, // Mobile-friendly
                loop: true, // Continuous rotation
                plugins: ["360", "drag"], // Enable drag interaction
                onFrame: function () {
                  // Optional: Haptic feedback for touch devices
                  if ("vibrate" in navigator) {
                    navigator.vibrate(10);
                  }
                },
                onLoad: function () {
                  console.log("SpriteSpin loaded successfully");
                },
                onError: function () {
                  showError("Error initializing SpriteSpin");
                },
              })
              .bind("onError", function () {
                showError("SpriteSpin initialization failed");
              });
          });
        });
      })(jQuery);
    </script>

    <!-- Navbar Functionality Scripts -->
    <script>
      // Comprehensive motorcycle data with brand-specific categories
      const motorcycleData = {
        pulsar: {
          classic: [
            {
              name: "PULSAR 220F ABS",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P220F",
            },
            {
              name: "PULSAR 150 TD",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150TD",
            },
            {
              name: "PULSAR 150",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150",
            },
            {
              name: "PULSAR 125",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P125",
            },
          ],
          ns: [
            {
              name: "PULSAR NS400Z",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS400Z",
            },
            {
              name: "PULSAR NS 200 ABS FI",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200FI",
            },
            {
              name: "PULSAR NS 200 ABS",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200",
            },
            {
              name: "PULSAR NS 200",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200",
            },
            {
              name: "PULSAR NS 160 ABS",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160",
            },
            {
              name: "PULSAR NS160 FI DUAL ABS BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160BS6",
            },
            {
              name: "PULSAR NS 125 BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS125BS6",
            },
            {
              name: "PULSAR NS 125 FI BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS125FIB",
            },
          ],
          n: [
            {
              name: "PULSAR N250",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N250",
            },
            {
              name: "PULSAR N160",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N160",
            },
          ],
        },
        dominar: {
          classic: [
            {
              name: "DOMINAR 400",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D400",
            },
            {
              name: "DOMINAR 250",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D250",
            },
          ],
        },
        avengers: {
          cruiser: [
            {
              name: "AVENGER CRUISE 220",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV220",
            },
            {
              name: "AVENGER STREET 160",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV160",
            },
          ],
        },
        discover: {
          commuter: [
            {
              name: "DISCOVER 125",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D125",
            },
            {
              name: "DISCOVER 110",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D110",
            },
          ],
        },
        platina: {
          commuter: [
            {
              name: "PLATINA 110",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL110",
            },
            {
              name: "PLATINA 100",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL100",
            },
          ],
        },
      };

      let currentCategory = "pulsar";
      let currentTab = "all";

      // DOM elements
      const mobileOverlay = document.getElementById("mobile-overlay");
      const mobileMenu = document.getElementById("mobile-menu");
      const mobileCategoryDetail = document.getElementById(
        "mobile-category-detail"
      );
      const mobileBikesSheet = document.getElementById("mobile-bikes-sheet");
      const mobileMenuBtn = document.getElementById("mobile-menu-btn");
      const mobileBikesBtn = document.getElementById("mobile-bikes-btn");
      const closeMobileMenu = document.getElementById("close-mobile-menu");
      const closeBikesSheet = document.getElementById("close-bikes-sheet");
      const backToCategories = document.getElementById("back-to-categories");
      const closeCategoryDetail = document.getElementById(
        "close-category-detail"
      );

      // Mobile Bikes Sheet Elements
      const mobileBikesHeader = document.getElementById("mobile-bikes-header");
      const backToBikesCategories = document.getElementById(
        "back-to-bikes-categories"
      );
      const mobileBikesCategoriesList = document.getElementById(
        "mobile-bikes-categories-list"
      );
      const mobileBikesTabsContainer = document.getElementById(
        "mobile-bikes-tabs-container"
      );
      const mobileBikesModelsContent = document.getElementById(
        "mobile-bikes-models-content"
      );

      // Function to format sub-category names
      function formatSubCategoryName(name) {
        if (name === "ns") return "NS";
        if (name === "n") return "N";
        if (name === "classic") return "Classic";
        if (name === "cruiser") return "Cruiser";
        if (name === "commuter") return "Commuter";
        return name.charAt(0).toUpperCase() + name.slice(1);
      }

      // Function to render tabs dynamically
      function renderTabs(category, isMobile = false, customContainer = null) {
        const tabsContainer = customContainer
          ? document.getElementById(customContainer)
          : isMobile
          ? document.getElementById("mobile-tabs-container")
          : document.getElementById("tabs-container");

        tabsContainer.innerHTML = "";

        // Get available tabs for this category
        const availableTabs = Object.keys(motorcycleData[category]).filter(
          (tab) => motorcycleData[category][tab].length > 0
        );

        // Always show "All" tab
        const allTab = document.createElement("button");
        allTab.className = `tab-btn ${
          currentTab === "all"
            ? "border-b-2 border-active-text font-medium active"
            : "text-active-text font-medium"
        } pb-1`;
        allTab.dataset.tab = "all";
        allTab.textContent = "All";
        tabsContainer.appendChild(allTab);

        // Create tabs for each available category
        availableTabs.forEach((tabName) => {
          const tab = document.createElement("button");
          tab.className = `tab-btn ${
            currentTab === tabName
              ? "border-b-2 border-active-text active font-medium"
              : "text-active-text font-medium"
          } pb-1`;
          tab.dataset.tab = tabName;
          tab.textContent = formatSubCategoryName(tabName);
          tabsContainer.appendChild(tab);
        });

        // Add event listeners to new tabs
        addTabEventListeners(isMobile);
      }

      // Function to render models
      function renderModels(
        category,
        tab,
        isMobile = false,
        customContainer = null
      ) {
        const container = customContainer
          ? document.getElementById(customContainer)
          : isMobile
          ? document.getElementById("mobile-models-list")
          : document.getElementById("models-content");

        let models = [];

        if (tab === "all") {
          // Create container HTML with category headings
          let containerHTML = "";

          // Get all sub-categories for this category
          const subCategories = Object.keys(motorcycleData[category]);

          // Iterate through each sub-category
          for (const subCategory of subCategories) {
            const subCategoryModels =
              motorcycleData[category][subCategory] || [];
            if (subCategoryModels.length === 0) continue;

            // Add category heading
            if (isMobile) {
              containerHTML += `
                            <div class="mt-4 mb-2 pl-3">
                                <div class="text-sm font-semibold text-accent border-b border-gray-200 pb-1">
                                    ${formatSubCategoryName(subCategory)}
                                </div>
                            </div>
                        `;
            } else {
              containerHTML += `
                            <div class="category-heading flex items-center gap-2 p-2">
                                <div class="w-1 h-6 bg-accent rounded-r-sm"></div>
                                    <span class="font-semibold text-black text-base">${formatSubCategoryName(
                                      subCategory
                                    )}</span>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="w-4 h-4 text-black ml-auto"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                        stroke-width="2"
                                    >
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                    </svg>
                            </div>
                        `;
            }

            // Add models for this sub-category
            subCategoryModels.forEach((model) => {
              if (isMobile) {
                containerHTML += `
                                <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                                    <img src="${model.image}" alt="${model.name}" class="w-16 h-10 object-cover rounded">
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-800">${model.name}</div>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            `;
              } else {
                containerHTML += `
                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                    <img src="${model.image}" alt="${model.name}" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                    <p class="text-xs font-medium text-gray-800 mb-1">${model.name}</p>
                                </div>
                            `;
              }
            });
          }

          container.innerHTML = containerHTML;
        } else {
          // For specific tabs, just show models without headings
          models = motorcycleData[category][tab] || [];

          if (isMobile) {
            container.innerHTML = models
              .map(
                (model) => `
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="${model.image}" alt="${model.name}" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">${model.name}</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    `
              )
              .join("");
          } else {
            container.innerHTML = models
              .map(
                (model) => `
                        <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                            <img src="${model.image}" alt="${model.name}" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                            <p class="text-xs font-medium text-gray-800 mb-1">${model.name}</p>
                        </div>
                    `
              )
              .join("");
          }

          // Show message if no models found
          if (models.length === 0) {
            container.innerHTML = `
                        <div class="${
                          isMobile
                            ? "text-center py-8 text-gray-500"
                            : "col-span-full text-center py-8 text-gray-500"
                        }">
                            No models available in this category
                        </div>
                    `;
          }
        }
      }

      // Initialize with default category and tab
      renderTabs(currentCategory);
      renderModels(currentCategory, currentTab);

      // Add tab event listeners
      function addTabEventListeners(isMobile = false) {
        const selector = isMobile
          ? "#mobile-tabs-container .tab-btn"
          : "#tabs-container .tab-btn";
        const tabs = document.querySelectorAll(selector);

        tabs.forEach((tab) => {
          tab.addEventListener("click", () => {
            const tabName = tab.dataset.tab;
            currentTab = tabName;

            // Update active tab styling
            tabs.forEach((t) => {
              t.classList.remove(
                "text-accent",
                "border-b-2",
                "border-accent",
                "active"
              );
              t.classList.add("text-gray-500");
            });
            tab.classList.remove("text-gray-500");
            tab.classList.add(
              "text-accent",
              "border-b-2",
              "border-accent",
              "active"
            );

            renderModels(currentCategory, tabName, isMobile);
          });
        });
      }

      // Mobile menu functionality
      // Open mobile menu
      mobileMenuBtn.addEventListener("click", () => {
        mobileMenu.classList.remove("hidden");
        mobileMenu.classList.remove("-translate-x-full");
        mobileOverlay.classList.remove("hidden");
        document.body.style.overflow = "hidden";
      });

      // Close mobile menu
      function closeMobileMenuFunc() {
        mobileMenu.classList.add("-translate-x-full");
        // Add a small delay before hiding to allow the slide animation to complete
        setTimeout(() => {
          mobileMenu.classList.add("hidden");
        }, 300);
        mobileCategoryDetail.classList.add("-translate-x-full");
        mobileBikesSheet.classList.add("translate-x-full");
        mobileOverlay.classList.add("hidden");
        document.body.style.overflow = "auto";
      }

      closeMobileMenu.addEventListener("click", closeMobileMenuFunc);
      mobileOverlay.addEventListener("click", closeMobileMenuFunc);

      // Handle window resize to ensure mobile menu is hidden on desktop
      window.addEventListener("resize", () => {
        if (window.innerWidth >= 1024) {
          // Close mobile menu when switching to desktop view
          closeMobileMenuFunc();
        }
      });

      // Mobile dropdown functionality
      document.querySelectorAll(".mobile-dropdown-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const content = btn.nextElementSibling;
          const icon = btn.querySelector("i");

          if (content.classList.contains("hidden")) {
            content.classList.remove("hidden");
            icon.classList.remove("fa-chevron-right");
            icon.classList.add("fa-chevron-down");
          } else {
            content.classList.add("hidden");
            icon.classList.remove("fa-chevron-down");
            icon.classList.add("fa-chevron-right");
          }
        });
      });

      // Mobile category selection (show panel)
      document.querySelectorAll(".mobile-category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Update category title
          document.getElementById(
            "category-title"
          ).textContent = `BIKES / ${category.toUpperCase()}`;

          // Render tabs and models for mobile
          renderTabs(category, true);
          renderModels(category, "all", true);

          // Hide overlay when showing model list
          mobileOverlay.classList.add("hidden");

          // Transition to model view
          mobileMenu.classList.add("-translate-x-full");
          setTimeout(() => {
            mobileCategoryDetail.classList.remove("hidden");
            mobileCategoryDetail.classList.remove("-translate-x-full");
          }, 50);
        });
      });

      // Back to categories (hide panel)
      backToCategories.addEventListener("click", () => {
        mobileCategoryDetail.classList.add("-translate-x-full");
        setTimeout(() => {
          mobileCategoryDetail.classList.add("hidden");
          mobileMenu.classList.remove("-translate-x-full");
          mobileOverlay.classList.remove("hidden");
        }, 300); // match transition duration
      });

      // Close category detail (hide panel)
      closeCategoryDetail.addEventListener("click", () => {
        mobileCategoryDetail.classList.add("-translate-x-full");
        setTimeout(() => {
          mobileCategoryDetail.classList.add("hidden");
          mobileMenu.classList.remove("-translate-x-full");
          mobileOverlay.classList.add("hidden");
          document.body.style.overflow = "auto";
        }, 300);
      });

      // Desktop category switching
      document.querySelectorAll(".category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Remove active class from all buttons
          document.querySelectorAll(".category-btn").forEach((b) => {
            b.classList.remove("active");
          });

          // Add active class to clicked button
          btn.classList.add("active");

          // Render new tabs and models
          renderTabs(category);
          renderModels(category, "all");
        });
      });

      // Add click handlers for model items
      document.addEventListener("click", (e) => {
        if (
          e.target.closest(".model-item") ||
          e.target.closest(".mobile-model-item")
        ) {
          const modelElement = e.target.closest(
            ".model-item, .mobile-model-item"
          );
          const modelName =
            modelElement.querySelector("p, .text-sm").textContent;
          alert(
            `You selected: ${modelName}\n\nThis would typically navigate to the model details page.`
          );
        }
      });

      // Initialize tabs with event listeners
      addTabEventListeners();

      // Mobile BIKES sheet functionality
      // Function to reset bikes sheet to initial state (category list)
      const resetBikesSheet = () => {
        mobileBikesHeader.textContent = "BIKES";
        mobileBikesCategoriesList.classList.remove("hidden");
        backToBikesCategories.classList.add("hidden");
        mobileBikesTabsContainer.classList.add("hidden");
        mobileBikesModelsContent.classList.add("hidden");
        mobileBikesSheet.classList.add("hidden");
      };

      // Open mobile bikes sheet
      mobileBikesBtn.addEventListener("click", () => {
        mobileBikesSheet.classList.remove("hidden", "translate-x-full");
        mobileOverlay.classList.remove("hidden");
        document.body.style.overflow = "hidden";
      });

      // Close mobile bikes sheet
      const closeBikesSheetFunc = () => {
        mobileBikesSheet.classList.add("translate-x-full");
        if (
          document
            .getElementById("mobile-menu")
            .classList.contains("-translate-x-full")
        ) {
          mobileOverlay.classList.add("hidden");
          document.body.style.overflow = "auto";
        }
        setTimeout(resetBikesSheet, 300); // Reset after transition
      };
      closeBikesSheet.addEventListener("click", closeBikesSheetFunc);

      // Mobile bikes category selection from sheet
      document.querySelectorAll(".mobile-bikes-category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Update UI for model view
          mobileBikesHeader.textContent = category.toUpperCase();
          mobileBikesCategoriesList.classList.add("hidden");
          backToBikesCategories.classList.remove("hidden");
          mobileBikesTabsContainer.classList.remove("hidden");
          mobileBikesModelsContent.classList.remove("hidden");

          // Render content for selected category
          renderTabs(category, true, "mobile-bikes-tabs-container");
          renderModels(category, "all", true, "mobile-bikes-models-content");
        });
      });

      // Back to bikes categories
      backToBikesCategories.addEventListener("click", resetBikesSheet);

      // Mobile bikes tab switching
      document.addEventListener("click", (e) => {
        if (e.target.closest("#mobile-bikes-tabs-container .tab-btn")) {
          const tab = e.target.closest("#mobile-bikes-tabs-container .tab-btn");
          const tabName = tab.dataset.tab;
          currentTab = tabName;

          // Update active tab styling
          document
            .querySelectorAll("#mobile-bikes-tabs-container .tab-btn")
            .forEach((t) => {
              t.classList.remove("border-b-2", "border-accent", "active");
              t.classList.add("text-gray-500");
            });
          tab.classList.remove("text-gray-500");
          tab.classList.add(
            "text-accent",
            "border-b-2",
            "border-accent",
            "active"
          );

          renderModels(
            currentCategory,
            tabName,
            true,
            "mobile-bikes-models-content"
          );
        }
      });
    </script>
  </body>
</html>
